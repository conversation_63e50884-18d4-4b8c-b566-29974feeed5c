#!/bin/bash
# =============================================================================
# Mem0 容器启动脚本 (简化版本)
# 参考备份配置，简化权限处理，避免复杂的用户切换逻辑
# =============================================================================

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[ENTRYPOINT]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[ENTRYPOINT]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[ENTRYPOINT]${NC} $1"
}

log_error() {
    echo -e "${RED}[ENTRYPOINT]${NC} $1"
}

# 创建必要的目录结构（简化版本）
create_directories() {
    log_info "Creating directory structure..."
    
    local directories=(
        "/app/data"
        "/app/data/mem0"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            log_info "Creating directory: $dir"
            mkdir -p "$dir" 2>/dev/null || {
                log_warning "Could not create directory: $dir (will be created by application)"
                continue
            }
        fi
    done
    
    log_success "Directory structure created"
}

# 等待依赖服务（简化版本）
wait_for_dependencies() {
    log_info "Checking service dependencies..."
    
    # 简单的服务检查，避免复杂的网络检测
    local services=()
    
    # 检查 Qdrant（如果启用）
    if [ "${WAIT_FOR_QDRANT:-true}" = "true" ]; then
        services+=("qdrant:6333")
    fi
    
    # 检查 Neo4j（如果启用）
    if [ "${WAIT_FOR_NEO4J:-true}" = "true" ]; then
        services+=("neo4j:7687")
    fi
    
    # 等待服务启动
    for service in "${services[@]}"; do
        local host=$(echo "$service" | cut -d: -f1)
        local port=$(echo "$service" | cut -d: -f2)
        
        log_info "Waiting for $host:$port..."
        
        local max_attempts=30
        local attempt=0
        
        while [ $attempt -lt $max_attempts ]; do
            if timeout 2 bash -c "echo > /dev/tcp/$host/$port" 2>/dev/null; then
                log_success "$host:$port is ready"
                break
            fi
            
            ((attempt++))
            if [ $attempt -eq $max_attempts ]; then
                log_warning "$host:$port is not ready after ${max_attempts}s, continuing anyway"
            else
                sleep 1
            fi
        done
    done
}

# 验证环境配置（简化版本）
validate_environment() {
    log_info "Validating environment configuration..."
    
    # 检查Python环境
    if command -v python >/dev/null; then
        python_version=$(python --version 2>&1)
        log_info "Python version: $python_version"
    else
        log_error "Python not found"
        exit 1
    fi
    
    # 显示重要配置
    log_info "Configuration summary:"
    log_info "  Data path: ${MEM0_DATA_PATH:-/app/data}"
    log_info "  History DB: ${HISTORY_DB_PATH:-/app/data/history.db}"
    log_info "  Python path: ${PYTHONPATH:-default}"
    log_info "  Working directory: $(pwd)"
    log_info "  User: $(whoami) ($(id 2>/dev/null || echo 'id command failed'))"
    
    log_success "Environment validation completed"
}

# 主函数
main() {
    log_info "Mem0 Container Entrypoint (Simplified Version)"
    log_info "=============================================="
    
    # 执行初始化步骤（简化版本）
    create_directories
    validate_environment
    
    # 等待依赖服务（如果在Docker网络中）
    if [ "${WAIT_FOR_DEPENDENCIES:-true}" = "true" ]; then
        wait_for_dependencies
    fi
    
    log_success "Container initialization completed successfully"
    echo
    
    # 如果没有提供命令，显示帮助信息
    if [ $# -eq 0 ]; then
        log_info "No command provided. Available options:"
        log_info "  uvicorn main:app --host 0.0.0.0 --port 8000"
        log_info "  python main.py"
        log_info "  bash"
        exit 0
    fi
    
    # 直接执行命令（参考备份配置的简化方式）
    log_info "Executing command: $*"
    echo
    
    # 设置工作目录
    cd /app/src || {
        log_warning "Could not change to /app/src, using current directory"
    }
    
    # 直接执行命令，不进行用户切换
    exec "$@"
}

# 信号处理
cleanup() {
    log_info "Received shutdown signal, cleaning up..."
    exit 0
}

# 注册信号处理
trap cleanup SIGTERM SIGINT

# 错误处理
error_handler() {
    local line_number=$1
    log_error "Script failed at line $line_number"
    exit 1
}

# 注册错误处理
trap 'error_handler $LINENO' ERR

# 执行主函数
main "$@"