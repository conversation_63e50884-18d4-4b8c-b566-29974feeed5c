#!/usr/bin/env python3
"""
验证Dify集成插件在API优化后的兼容性
Validate Dify integration plugin compatibility after API optimization
"""

import os
import re

def check_dify_api_endpoints():
    """检查Dify插件中的API端点使用情况"""
    print("🔍 检查Dify集成插件API端点兼容性...")
    
    plugin_dir = "/opt/mem0ai/mem0-dify-integrated"
    
    if not os.path.exists(plugin_dir):
        print(f"❌ 找不到Dify插件目录: {plugin_dir}")
        return False
    
    # 需要检查的旧端点
    old_endpoints = [
        "graph/entities/",
        "graph/search/", 
        "/v1/graph/entities/",
        "/v1/graph/search/"
    ]
    
    # 检查所有Python文件
    found_old_endpoints = []
    used_endpoints = set()
    
    for root, dirs, files in os.walk(plugin_dir):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                    # 检查旧端点
                    for endpoint in old_endpoints:
                        if endpoint in content:
                            found_old_endpoints.append((file_path, endpoint))
                    
                    # 提取使用的端点
                    api_pattern = r'f?["\']([^"\']*?)/v1/([^"\']*?)["\']'
                    matches = re.findall(api_pattern, content)
                    for base, endpoint in matches:
                        if 'v1' in endpoint or endpoint.startswith('/'):
                            used_endpoints.add(f"/v1/{endpoint}")
    
    print(f"📊 API端点使用统计:")
    
    if found_old_endpoints:
        print(f"❌ 发现旧的图谱端点引用:")
        for file_path, endpoint in found_old_endpoints:
            rel_path = os.path.relpath(file_path, plugin_dir)
            print(f"   {rel_path}: {endpoint}")
        return False
    else:
        print(f"✅ 未发现旧的图谱端点引用")
    
    if used_endpoints:
        print(f"📋 使用的API端点:")
        for endpoint in sorted(used_endpoints):
            print(f"   {endpoint}")
    
    # 检查实际使用的端点（更精确的提取）
    print(f"\n🔍 详细端点分析:")
    for root, dirs, files in os.walk(plugin_dir):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, plugin_dir)
                
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                    # 查找API调用
                    api_calls = re.findall(r'f?["\']([^"\']*?/v1/[^"\']*?)["\']', content)
                    if api_calls:
                        print(f"   {rel_path}:")
                        for call in api_calls:
                            if 'api_url' not in call:  # 排除变量引用
                                print(f"      - {call}")
    
    return True

def check_dify_plugin_structure():
    """检查Dify插件结构完整性"""
    print("\n🔍 检查Dify插件结构完整性...")
    
    plugin_dir = "/opt/mem0ai/mem0-dify-integrated"
    
    required_files = [
        "manifest.yaml",
        "main.py", 
        "provider/mem0.py",
        "provider/mem0.yaml"
    ]
    
    required_tools = [
        "tools/add_memory.py",
        "tools/retrieve_memory.py", 
        "tools/update_memory.py",
        "tools/delete_memory.py"
    ]
    
    missing_files = []
    
    for file_path in required_files + required_tools:
        full_path = os.path.join(plugin_dir, file_path)
        if os.path.exists(full_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - 缺失")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def check_dify_graph_feature_support():
    """检查Dify插件是否需要图谱功能支持"""
    print("\n🔍 检查Dify插件图谱功能需求...")
    
    plugin_dir = "/opt/mem0ai/mem0-dify-integrated"
    
    # 检查是否有图谱相关的配置或工具
    graph_keywords = [
        "enable_graph",
        "graph_memory", 
        "relations",
        "entities",  # 注意：这里可能是误报，因为可能是Dify框架的entities
        "relationships",
        "graph_search"
    ]
    
    graph_references = []
    
    for root, dirs, files in os.walk(plugin_dir):
        for file in files:
            if file.endswith(('.py', '.yaml')):
                file_path = os.path.join(root, file)
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                    for keyword in graph_keywords:
                        if keyword in content and 'dify_plugin.entities' not in content:
                            # 排除Dify框架本身的entities导入
                            graph_references.append((os.path.relpath(file_path, plugin_dir), keyword))
    
    if graph_references:
        print(f"⚠️  发现可能的图谱功能引用:")
        for file_path, keyword in graph_references:
            print(f"   {file_path}: {keyword}")
        print(f"💡 建议检查这些引用是否需要适配新的图谱API端点")
        return False
    else:
        print(f"✅ 未发现图谱功能引用，无需适配")
        return True

def main():
    """主验证函数"""
    print("=" * 70)
    print("Dify集成插件 API 优化后兼容性验证")
    print("=" * 70)
    
    checks = [
        ("API端点兼容性", check_dify_api_endpoints),
        ("插件结构完整性", check_dify_plugin_structure),
        ("图谱功能需求", check_dify_graph_feature_support)
    ]
    
    results = []
    
    for check_name, check_func in checks:
        print(f"\n🔧 执行检查: {check_name}")
        try:
            result = check_func()
            results.append((check_name, result))
            if result:
                print(f"✅ {check_name} - 通过")
            else:
                print(f"⚠️  {check_name} - 需要关注")
        except Exception as e:
            print(f"❌ {check_name} - 错误: {e}")
            results.append((check_name, False))
    
    print("\n" + "=" * 70)
    print("验证结果汇总:")
    print("=" * 70)
    
    all_compatible = True
    for check_name, result in results:
        status = "✅ 兼容" if result else "⚠️  需要关注"
        print(f"  {check_name}: {status}")
        if not result:
            all_compatible = False
    
    print("\n" + "=" * 70)
    if all_compatible:
        print("🎉 Dify集成插件与API优化完全兼容！")
        print("\n📋 兼容性总结:")
        print("   • 使用标准内存管理API端点")
        print("   • 未使用重命名的图谱API端点")
        print("   • 插件结构完整")
        print("   • 无需额外适配工作")
    else:
        print("⚠️  Dify集成插件部分检查需要关注，但整体兼容性良好。")
        print("\n💡 建议:")
        print("   • 检查标记的引用是否确实需要图谱功能")
        print("   • 如需图谱功能，可考虑添加相应工具")
        print("   • 当前版本可以正常使用标准功能")
    print("=" * 70)

if __name__ == "__main__":
    main()