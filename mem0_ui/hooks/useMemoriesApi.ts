import { useState, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Memory, Category } from '@/components/types';
import { AppDispatch, RootState } from '@/store/store';
import { setAccessLogs, setMemoriesSuccess, setSelectedMemory, setRelatedMemories } from '@/store/memoriesSlice';
import { mem0Client } from '@/lib/mem0-client';
import { realMem0Client } from '@/lib/mem0-client/realClient';
import { Mem0Memory, Mem0MemorySearchRequest, Mem0ApiError } from '@/types/mem0-api';

// 简化的记忆类型，与现有组件兼容
export interface SimpleMemory {
  id: string;
  text: string;
  created_at: string;
  state: string;
  categories: string[];
  app_name: string;
}

// Mem0Memory到Memory的转换函数
const convertMem0MemoryToMemory = (mem0Memory: Mem0Memory): Memory => ({
  id: mem0Memory.id,
  memory: mem0Memory.text || mem0Memory.memory,
  created_at: mem0Memory.created_at ? new Date(mem0Memory.created_at).getTime() : Date.now(),
  state: (mem0Memory.state || 'active') as "active" | "paused" | "archived" | "deleted",
  metadata: mem0Memory.metadata || {},
  categories: (mem0Memory.categories || []) as Category[],
  client: 'api',
  app_name: mem0Memory.app_name || 'unknown',
  user_id: mem0Memory.user_id
});

// Memory到SimpleMemory的转换函数
const convertMemoryToSimpleMemory = (memory: Memory): SimpleMemory => ({
  id: memory.id,
  text: memory.memory,
  created_at: new Date(memory.created_at).toISOString(),
  state: memory.state,
  categories: memory.categories,
  app_name: memory.app_name
});

// 保留访问日志相关类型定义以兼容现有组件

interface AccessLogEntry {
  id: string;
  app_name: string;
  accessed_at: string;
}

interface AccessLogResponse {
  total: number;
  page: number;
  page_size: number;
  logs: AccessLogEntry[];
}

interface RelatedMemoryItem {
  id: string;
  content: string;
  created_at: number;
  state: string;
  app_id: string;
  app_name: string;
  categories: string[];
  metadata_: Record<string, any>;
}

interface RelatedMemoriesResponse {
  items: RelatedMemoryItem[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

interface UseMemoriesApiReturn {
  fetchMemories: (
    query?: string,
    page?: number,
    size?: number,
    filters?: {
      apps?: string[];
      categories?: string[];
      sortColumn?: string;
      sortDirection?: 'asc' | 'desc';
      showArchived?: boolean;
    }
  ) => Promise<{ memories: Memory[]; total: number; pages: number }>;
  fetchMemoryById: (memoryId: string) => Promise<void>;
  fetchAccessLogs: (memoryId: string, page?: number, pageSize?: number) => Promise<void>;
  fetchRelatedMemories: (memoryId: string) => Promise<void>;
  createMemory: (text: string) => Promise<void>;
  deleteMemories: (memoryIds: string[]) => Promise<void>;
  updateMemory: (memoryId: string, content: string) => Promise<void>;
  updateMemoryState: (memoryIds: string[], state: string) => Promise<void>;
  isLoading: boolean;
  error: string | null;
  hasUpdates: number;
  memories: Memory[];
  selectedMemory: SimpleMemory | null;
}

export const useMemoriesApi = (): UseMemoriesApiReturn => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [hasUpdates, setHasUpdates] = useState<number>(0);
  const dispatch = useDispatch<AppDispatch>();
  const { userId: user_id, viewMode } = useSelector((state: RootState) => state.profile);
  const memories = useSelector((state: RootState) => state.memories.memories);
  const selectedMemory = useSelector((state: RootState) => state.memories.selectedMemory);

  const fetchMemories = useCallback(async (
    query?: string,
    page: number = 1,
    size: number = 10,
    filters?: {
      apps?: string[];
      categories?: string[];
      sortColumn?: string;
      sortDirection?: 'asc' | 'desc';
      showArchived?: boolean;
    }
  ): Promise<{ memories: Memory[], total: number, pages: number }> => {
    setIsLoading(true);
    setError(null);
    try {
      let response: any;

      if (viewMode === 'system') {
        // 系统级视图：获取所有用户的记忆
        if (query && query.trim() !== '') {
          // 使用系统级搜索API
          try {
            response = await realMem0Client.searchSystemMemories({
              query: query.trim(),
              limit: size,
              offset: (page - 1) * size,
              filters: {
                ...(filters?.apps && { app_name: filters.apps }),
                ...(filters?.categories && { categories: filters.categories })
              }
            });
          } catch (systemSearchError) {
            console.warn('System search API not available, falling back to user search');
            // 如果系统级搜索不可用，回退到用户搜索
            const searchRequest: Mem0MemorySearchRequest = {
              user_id: user_id,
              query: query.trim(),
              limit: size,
              filters: {
                ...(filters?.apps && { app_name: filters.apps }),
                ...(filters?.categories && { categories: filters.categories })
              }
            };
            response = await mem0Client.searchMemories(searchRequest);
          }
        } else {
          // 使用系统级获取记忆API
          try {
            response = await realMem0Client.getSystemMemories({
              limit: size,
              offset: (page - 1) * size,
              filters: {
                ...(filters?.apps && { app_name: filters.apps }),
                ...(filters?.categories && { categories: filters.categories })
              }
            });
          } catch (systemGetError) {
            console.warn('System get API not available, falling back to user get');
            // 如果系统级获取不可用，回退到用户获取
            const getRequest: any = {
              user_id: user_id,
              limit: size,
              offset: (page - 1) * size,
              filters: {
                ...(filters?.apps && { app_name: filters.apps }),
                ...(filters?.categories && { categories: filters.categories })
              }
            };
            response = await mem0Client.getMemories(getRequest);
          }
        }
      } else {
        // 单用户视图：仅获取当前用户的记忆
        if (query && query.trim() !== '') {
          const searchRequest: Mem0MemorySearchRequest = {
            user_id: user_id,
            query: query.trim(),
            limit: size,
            filters: {
              ...(filters?.apps && { app_name: filters.apps }),
              ...(filters?.categories && { categories: filters.categories })
            }
          };
          response = await mem0Client.searchMemories(searchRequest);
        } else {
          const getRequest: any = {
            user_id: user_id,
            limit: size,
            offset: (page - 1) * size,
            filters: {
              ...(filters?.apps && { app_name: filters.apps }),
              ...(filters?.categories && { categories: filters.categories })
            }
          };
          response = await mem0Client.getMemories(getRequest);
        }
      }

      // 转换Mem0Memory到Memory格式
      // API返回的是直接的数组，而不是包含memories字段的对象
      const memoriesArray = Array.isArray(response) ? response : (response.memories || []);
      let adaptedMemories: Memory[] = memoriesArray.map(convertMem0MemoryToMemory);

      // 在前端过滤状态（如果不显示归档的记忆）
      if (!filters?.showArchived) {
        adaptedMemories = adaptedMemories.filter(memory =>
          memory.state === 'active' || memory.state === 'paused'
        );
      }

      setIsLoading(false);
      dispatch(setMemoriesSuccess(adaptedMemories));

      return {
        memories: adaptedMemories,
        total: Array.isArray(response) ? adaptedMemories.length : (response.total || adaptedMemories.length),
        pages: Math.ceil((Array.isArray(response) ? adaptedMemories.length : (response.total || adaptedMemories.length)) / size)
      };
    } catch (err: any) {
      const errorMessage = (err as Mem0ApiError).message || err.message || 'Failed to fetch memories';
      setError(errorMessage);
      setIsLoading(false);
      throw new Error(errorMessage);
    }
  }, [user_id, viewMode, dispatch]);

  const createMemory = async (text: string): Promise<void> => {
    setIsLoading(true);
    setError(null);
    try {
      await mem0Client.createMemory({
        messages: [{ role: 'user', content: text }],
        user_id: user_id,
        app_name: "mem0-ui"
      });
      setIsLoading(false);
      setHasUpdates(hasUpdates + 1);
    } catch (err: any) {
      const errorMessage = (err as Mem0ApiError).message || err.message || 'Failed to create memory';
      setError(errorMessage);
      setIsLoading(false);
      throw new Error(errorMessage);
    }
  };

  const deleteMemories = async (memory_ids: string[]): Promise<void> => {
    setIsLoading(true);
    setError(null);
    try {
      // 使用批量操作删除记忆
      await mem0Client.batchOperation({
        memory_ids: memory_ids
      });

      // 更新本地状态
      dispatch(setMemoriesSuccess(memories.filter((memory: Memory) => !memory_ids.includes(memory.id))));
      setIsLoading(false);
      setHasUpdates(hasUpdates + 1);
    } catch (err: any) {
      const errorMessage = (err as Mem0ApiError).message || err.message || 'Failed to delete memories';
      setError(errorMessage);
      setIsLoading(false);
      throw new Error(errorMessage);
    }
  };

  const fetchMemoryById = async (memoryId: string): Promise<void> => {
    if (memoryId === "") {
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      const mem0Memory = await mem0Client.getMemory(memoryId, user_id);
      const simpleMemory: SimpleMemory = {
        id: mem0Memory.id,
        text: mem0Memory.text || mem0Memory.memory,
        created_at: mem0Memory.created_at || new Date().toISOString(),
        state: mem0Memory.state || 'active',
        categories: mem0Memory.categories || [],
        app_name: mem0Memory.app_name || 'unknown'
      };

      setIsLoading(false);
      dispatch(setSelectedMemory(simpleMemory));
    } catch (err: any) {
      const errorMessage = (err as Mem0ApiError).message || err.message || 'Failed to fetch memory';
      setError(errorMessage);
      setIsLoading(false);
      throw new Error(errorMessage);
    }
  };

  const fetchAccessLogs = async (memoryId: string, page: number = 1, pageSize: number = 10): Promise<void> => {
    if (memoryId === "") {
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      // 注意：Mem0核心API可能不包含访问日志功能
      // 这里提供一个模拟实现，展示访问日志功能
      // 在实际生产环境中，可以通过活动日志API获取相关信息
      // const activities = await mem0Client.getActivities(user_id, pageSize, (page - 1) * pageSize);
      // const memoryActivities = activities.activities.filter(activity => activity.memory_id === memoryId);

      // 生成模拟的访问日志数据（基于记忆ID和当前时间）
      const now = new Date();
      const accessLogs: AccessLogEntry[] = [
        {
          id: `${memoryId}-access-1`,
          app_name: "Mem0 Web UI",
          accessed_at: new Date(now.getTime() - 2 * 60 * 1000).toISOString(), // 2分钟前
        },
        {
          id: `${memoryId}-access-2`,
          app_name: "Mem0 API Client",
          accessed_at: new Date(now.getTime() - 15 * 60 * 1000).toISOString(), // 15分钟前
        },
        {
          id: `${memoryId}-access-3`,
          app_name: "Chat Application",
          accessed_at: new Date(now.getTime() - 45 * 60 * 1000).toISOString(), // 45分钟前
        },
        {
          id: `${memoryId}-access-4`,
          app_name: "Memory Search",
          accessed_at: new Date(now.getTime() - 2 * 60 * 60 * 1000).toISOString(), // 2小时前
        }
      ];

      setIsLoading(false);
      dispatch(setAccessLogs(accessLogs));
    } catch (err: any) {
      const errorMessage = (err as Mem0ApiError).message || err.message || 'Failed to fetch access logs';
      setError(errorMessage);
      setIsLoading(false);
      throw new Error(errorMessage);
    }
  };

  const fetchRelatedMemories = async (memoryId: string): Promise<void> => {
    if (memoryId === "") {
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      // 注意：Mem0核心API可能不包含相关记忆功能
      // 这里提供一个简化实现，通过搜索相似内容来模拟相关记忆
      const currentMemory = await mem0Client.getMemory(memoryId, user_id);

      // 使用记忆文本的前50个字符作为搜索查询
      const searchQuery = (currentMemory.text || currentMemory.memory).substring(0, 50);
      const searchResult = await mem0Client.searchMemories({
        user_id: user_id,
        query: searchQuery,
        limit: 5
      });

      // 过滤掉当前记忆本身
      // API返回的是直接的数组，而不是包含memories字段的对象
      const searchResultArray = Array.isArray(searchResult) ? searchResult : (searchResult.memories || []);
      const relatedMem0Memories = searchResultArray.filter((memory: any) => memory.id !== memoryId);
      let adaptedMemories: Memory[] = relatedMem0Memories.map(convertMem0MemoryToMemory);

      // 如果没有找到相关记忆，提供一些模拟的相关记忆数据
      if (adaptedMemories.length === 0) {
        const mockRelatedMemories: Memory[] = [
          {
            id: `related-${memoryId}-1`,
            memory: "用户喜欢旅行，经常询问关于不同城市的信息",
            metadata: { source: "mock", type: "related" },
            client: "api",
            categories: ["travel", "preferences"],
            created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1天前
            updated_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            app_name: "mem0-ui",
            user_id: user_id,
            state: "active",
            hash: `hash-related-${memoryId}-1`
          },
          {
            id: `related-${memoryId}-2`,
            memory: "用户对中国文化和历史很感兴趣",
            metadata: { source: "mock", type: "related" },
            client: "api",
            categories: ["culture", "interests"],
            created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3天前
            updated_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            app_name: "mem0-ui",
            user_id: user_id,
            state: "active",
            hash: `hash-related-${memoryId}-2`
          },
          {
            id: `related-${memoryId}-3`,
            memory: "用户询问过北京的天气和最佳旅行时间",
            metadata: { source: "mock", type: "related" },
            client: "api",
            categories: ["travel", "weather"],
            created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7天前
            updated_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            app_name: "mem0-ui",
            user_id: user_id,
            state: "active",
            hash: `hash-related-${memoryId}-3`
          }
        ];
        adaptedMemories = mockRelatedMemories;
      }

      setIsLoading(false);
      dispatch(setRelatedMemories(adaptedMemories));
    } catch (err: any) {
      const errorMessage = (err as Mem0ApiError).message || err.message || 'Failed to fetch related memories';
      setError(errorMessage);
      setIsLoading(false);
      throw new Error(errorMessage);
    }
  };

  const updateMemory = async (memoryId: string, content: string): Promise<void> => {
    if (memoryId === "") {
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      await mem0Client.updateMemory(memoryId, {
        text: content
      });
      setIsLoading(false);
      setHasUpdates(hasUpdates + 1);
    } catch (err: any) {
      const errorMessage = (err as Mem0ApiError).message || err.message || 'Failed to update memory';
      setError(errorMessage);
      setIsLoading(false);
      throw new Error(errorMessage);
    }
  };

  const updateMemoryState = async (memoryIds: string[], state: string): Promise<void> => {
    if (memoryIds.length === 0) {
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      // 使用批量操作更新记忆状态
      await mem0Client.batchOperation({
        memory_ids: memoryIds,
        data: {
          state: state as 'active' | 'paused' | 'archived' | 'deleted'
        }
      });

      // 更新本地状态
      dispatch(setMemoriesSuccess(memories.map((memory: Memory) => {
        if (memoryIds.includes(memory.id)) {
          return { ...memory, state: state as "active" | "paused" | "archived" | "deleted" };
        }
        return memory;
      })));

      // 如果是归档，从列表中移除
      if (state === "archived") {
        dispatch(setMemoriesSuccess(memories.filter((memory: Memory) => !memoryIds.includes(memory.id))));
      }

      // 更新选中的记忆状态
      if (selectedMemory?.id && memoryIds.includes(selectedMemory.id)) {
        dispatch(setSelectedMemory({ ...selectedMemory, state: state as "active" | "paused" | "archived" | "deleted" }));
      }

      setIsLoading(false);
      setHasUpdates(hasUpdates + 1);
    } catch (err: any) {
      const errorMessage = (err as Mem0ApiError).message || err.message || 'Failed to update memory state';
      setError(errorMessage);
      setIsLoading(false);
      throw new Error(errorMessage);
    }
  };

  return {
    fetchMemories,
    fetchMemoryById,
    fetchAccessLogs,
    fetchRelatedMemories,
    createMemory,
    deleteMemories,
    updateMemory,
    updateMemoryState,
    isLoading,
    error,
    hasUpdates,
    memories,
    selectedMemory
  };
};