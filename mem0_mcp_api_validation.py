#!/usr/bin/env python3
"""
验证MCP客户端在API优化后的兼容性
Validate MCP client compatibility after API optimization
"""

import os
import sys

def check_endpoint_updates():
    """检查MCP适配器文件中的端点更新"""
    print("🔍 检查MCP适配器端点更新...")
    
    adapter_file = "/opt/mem0ai/mem0_mcp/src/client/adapters.py"
    
    if not os.path.exists(adapter_file):
        print(f"❌ 找不到适配器文件: {adapter_file}")
        return False
    
    with open(adapter_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否还有旧的端点
    old_endpoints = content.count('graph/entities/')
    new_endpoints = content.count('graph/nodes/')
    
    print(f"📊 端点统计:")
    print(f"   旧端点 (graph/entities/): {old_endpoints} 个")
    print(f"   新端点 (graph/nodes/): {new_endpoints} 个")
    
    if old_endpoints > 0:
        print("❌ 发现旧端点引用，需要进一步更新")
        return False
    
    if new_endpoints == 0:
        print("❌ 未发现新端点引用，可能更新失败")
        return False
    
    # 检查具体的更新位置
    lines = content.split('\n')
    updated_lines = []
    
    for i, line in enumerate(lines, 1):
        if 'graph/nodes/' in line:
            method_context = ""
            # 找到所在的方法
            for j in range(max(0, i-10), i):
                if 'async def' in lines[j]:
                    method_context = lines[j].strip().split('(')[0].replace('async def ', '')
                    break
            updated_lines.append(f"   第{i}行: {method_context} 方法")
    
    print(f"✅ 已更新的方法位置:")
    for line in updated_lines:
        print(line)
    
    return True

def check_mcp_tools_integration():
    """检查MCP工具的集成情况"""
    print("\n🔍 检查MCP工具集成...")
    
    tools_file = "/opt/mem0ai/mem0_mcp/src/tools/memory_tools.py"
    graph_tools_file = "/opt/mem0ai/mem0_mcp/src/tools/graph_tools.py"
    
    files_to_check = [
        (tools_file, "内存工具"),
        (graph_tools_file, "图谱工具")
    ]
    
    for file_path, name in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {name}文件存在: {file_path}")
        else:
            print(f"❌ {name}文件缺失: {file_path}")
            return False
    
    return True

def check_api_compatibility():
    """检查API兼容性"""
    print("\n🔍 检查API兼容性...")
    
    # 检查服务器主文件中的端点更新
    server_file = "/opt/mem0ai/server/main.py"
    
    if not os.path.exists(server_file):
        print(f"❌ 找不到服务器文件: {server_file}")
        return False
    
    with open(server_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查新端点定义
    if '/v1/graph/nodes/' in content:
        print("✅ 服务器端已定义新的 /v1/graph/nodes/ 端点")
    else:
        print("❌ 服务器端未找到新的图谱节点端点")
        return False
    
    if '/v1/graph/query/' in content:
        print("✅ 服务器端已定义新的 /v1/graph/query/ 端点")
    else:
        print("❌ 服务器端未找到新的图谱查询端点")
        return False
    
    return True

def main():
    """主验证函数"""
    print("=" * 60)
    print("MCP API 优化后兼容性验证")
    print("=" * 60)
    
    checks = [
        ("MCP适配器端点更新", check_endpoint_updates),
        ("MCP工具集成", check_mcp_tools_integration), 
        ("API兼容性", check_api_compatibility)
    ]
    
    results = []
    
    for check_name, check_func in checks:
        print(f"\n🔧 执行检查: {check_name}")
        try:
            result = check_func()
            results.append((check_name, result))
            if result:
                print(f"✅ {check_name} - 通过")
            else:
                print(f"❌ {check_name} - 失败")
        except Exception as e:
            print(f"❌ {check_name} - 错误: {e}")
            results.append((check_name, False))
    
    print("\n" + "=" * 60)
    print("验证结果汇总:")
    print("=" * 60)
    
    all_passed = True
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {check_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有检查通过！MCP适配器已成功适配API优化。")
        print("\n📋 更新总结:")
        print("   • 图谱实体端点: graph/entities/ → graph/nodes/")
        print("   • 图谱搜索端点: graph/search/ → graph/query/") 
        print("   • MCP工具完整性: 完整")
        print("   • API兼容性: 兼容")
        print("   • 向后兼容性: 保持")
    else:
        print("⚠️  部分检查未通过，建议进一步检查相关实现。")
    print("=" * 60)

if __name__ == "__main__":
    main()