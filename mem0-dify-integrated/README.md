## Mem0 Dify Plugin

**Author:** blue (updated by AI Assistant)
**Version:** 2.0.0
**Type:** tool

### Description

Mem0 is a comprehensive memory management plugin that enables conversation history storage and retrieval for LLM applications. This plugin supports both V1 and V2 APIs with advanced features including:

- **Advanced Retrieval**: keyword_search, rerank, filter_memories
- **Multimodal Support**: Images (JPG, PNG) and documents (MDX, TXT, PDF)
- **Memory Management**: Update, version history, and comparison
- **Unified Architecture**: BaseMem0Tool for consistent error handling
- **Multilingual Support**: English, Chinese, Portuguese, Japanese

![workflow](./_assets/workflow.png)



### Setup

#### API Configuration

1. **API Key**:
   - **Cloud service**: Get your API key from [Mem0 Dashboard](https://app.mem0.ai/dashboard/api-keys)
   - **Local deployment**: Use any token (authentication is optional for local development)

2. **API URL Configuration**:
   - **Local deployment**: `http://localhost:8000` (default)
   - **Cloud service**: `https://api.mem0.ai`
   - **Custom deployment**: Your custom API endpoint

#### Installation

1. Install the package:
```bash
pip install mem0ai
```

3. Initialize the client:
```python
from mem0 import MemoryClient
client = MemoryClient(api_key="your-api-key")
```

![dashboard](./_assets/dashboard.png)

### Configuration

#### API Key
Required for authentication with the mem0 API.

#### API URL (Optional)
You can specify a custom API URL if you're using a self-hosted instance or a different endpoint:
- Default: `https://api.mem0.ai`
- Format: Enter the base URL without trailing slash (e.g., `https://your-custom-api.example.com`)

### Available Tools

The plugin provides both V1 and V2 API tools for maximum flexibility:

#### V1 Tools (Legacy Support)

##### add_mem0ai_memory
Stores conversation history and context for users using V1 API.

```python
messages = [
    {"role": "user", "content": "Hi, I'm Alex. I'm a vegetarian and I'm allergic to nuts."},
    {"role": "assistant", "content": "Hello Alex! I've noted your dietary preferences."}
]
client.add(messages, user_id="alex")
```

Backend logic:
- Messages are stored in user-specific partitions using `user_id`
- Supports conversation history and context storage
- Handles message format validation and processing
- Optimizes storage for efficient retrieval

#### retrieve_memory
Retrieves relevant conversation history based on queries.

```python
query = "What can I cook for dinner tonight?"
memories = client.search(query, user_id="alex")
```

Backend logic:
- Semantic search across user's memory partition
- Returns relevant conversation snippets
- Handles context ranking and relevance scoring
- Optimizes query performance

### Usage in Dify

1. In Dify workflows, place `retrieve_memory` before LLM calls to provide context
2. Add `add_memory` after LLM responses to store new interactions
3. `user_id` can be customized in workflow run API
4. Note: iframe and webapp modes currently don't support user_id due to lack of access control

#### V2 Tools (Enhanced Features)

> **Important Notice**: The `add_mem0ai_memory_v2` tool has been removed as V2 API does not have a separate memory creation endpoint. Please use `add_mem0ai_memory` (V1) for memory creation, which supports all necessary features including AI inference and metadata.

##### get_mem0ai_memories_v2
Retrieve memories using V2 API with advanced filtering, pagination, and sorting capabilities.

##### retrieve_mem0ai_memory_v2 (Search)
Advanced memory search with complex filtering and enhanced query capabilities.

##### delete_mem0ai_memory
Delete specific memories by their unique ID.

**Parameters:**
- `user`: User message content
- `assistant`: Assistant response content
- `user_id`: User identifier (required)
- `agent_id`: Agent identifier (optional)
- `run_id`: Session/run identifier (optional)
- `infer`: Enable AI inference (default: true)
- `metadata`: JSON metadata for additional context (optional)

**Example:**
```json
{
  "user": "I love Italian food",
  "assistant": "I'll remember your preference for Italian cuisine",
  "user_id": "user123",
  "agent_id": "food_assistant",
  "infer": true,
  "metadata": "{\"category\": \"food_preferences\", \"confidence\": 0.9}"
}
```

##### retrieve_mem0ai_memory_v2
Advanced memory retrieval with filtering and pagination.

**Parameters:**
- `query`: Search query (required)
- `user_id`: User identifier (required)
- `agent_id`: Filter by agent (optional)
- `run_id`: Filter by session (optional)
- `limit`: Maximum results (1-100, default: 10)
- `similarity_threshold`: Minimum similarity score (0.0-1.0, default: 0.0)
- `filters`: Advanced JSON filters (optional)

**Example:**
```json
{
  "query": "food preferences",
  "user_id": "user123",
  "limit": 5,
  "similarity_threshold": 0.7,
  "filters": "{\"AND\": [{\"categories\": [\"food\"]}, {\"metadata.confidence\": {\">=\": 0.8}}]}"
}
```

### V1 vs V2 Feature Comparison

| Feature | V1 API | V2 API |
|---------|--------|--------|
| Basic memory operations | ✅ | ✅ |
| Agent ID support | ❌ | ✅ |
| Run ID support | ❌ | ✅ |
| AI inference | ❌ | ✅ |
| Metadata support | ❌ | ✅ |
| Advanced filtering | ❌ | ✅ |
| Pagination | ❌ | ✅ |
| Similarity threshold | ❌ | ✅ |

### Best Practices

1. **Use V2 for new implementations** - V2 provides enhanced features and better performance
2. **Enable inference for better insights** - V2's AI inference extracts meaningful patterns
3. **Use metadata for context** - Store additional context in JSON metadata
4. **Implement proper filtering** - Use agent_id and run_id for better organization
5. **Set appropriate similarity thresholds** - Filter results by relevance score

### Workflow Integration

1. In Dify workflows, place `retrieve_memory_v2` before LLM calls to provide context
2. Add `add_memory` after LLM responses to store new interactions (supports inference and metadata)
3. Use `agent_id` to separate different AI assistants' memories
4. Use `run_id` to track conversation sessions
5. Leverage metadata for rich context and filtering

### Migration from V1 to V2

V1 and V2 tools can coexist in the same workflow. Migration guidelines:

1. **Memory Creation**: Continue using `add_mem0ai_memory` (V1) - this is the only available memory creation tool
2. **Memory Retrieval**: Replace `retrieve_mem0ai_memory` with `retrieve_mem0ai_memory_v2` for enhanced search capabilities
3. **Memory Management**: Use `get_mem0ai_memories_v2` for advanced memory listing with filtering
4. Add optional parameters like `agent_id`, `infer`, and `metadata` as needed
5. Update search queries to use advanced filtering if required

> **Note**: The `add_mem0ai_memory_v2` tool has been removed as it was incorrectly implemented. V2 API uses contextual adding through the V1 endpoint.

### API Compatibility

This plugin is designed to work with both:

1. **Local Mem0 Deployment** (Default)
   - API URL: `http://localhost:8000`
   - V1 API: Full compatibility with add/search operations
   - V2 API: Search operations with advanced filtering
   - Authentication: Optional (any token works for development)

2. **Mem0 Cloud Service**
   - API URL: `https://api.mem0.ai`
   - Requires valid API key from Mem0 Dashboard
   - Full V1 and V2 API support

#### API Endpoint Mapping

| Plugin Tool | API Endpoint | Method | Description |
|-------------|--------------|--------|-------------|
| `add_mem0ai_memory` | `/v1/memories` | POST | V1 memory creation |
| `retrieve_mem0ai_memory` | `/v1/memories/search` | POST | V1 memory search |
| `add_mem0ai_memory_v2` | `/v1/memories` | POST | V2-style memory creation (uses V1 endpoint) |
| `retrieve_mem0ai_memory_v2` | `/v2/memories/search` | POST | V2 advanced memory search |
| `get_mem0ai_memories_v2` | `/v2/memories` | POST | V2 memory retrieval with advanced filtering |
| `delete_mem0ai_memory` | `/v1/memories/{memory_id}` | DELETE | Delete specific memory by ID |

**Notes**:
- Memory creation uses V1 API endpoint (`/v1/memories`) - V2 API does not provide a separate creation endpoint
- V2 memory retrieval supports advanced filtering, pagination, and sorting capabilities
- Memory deletion uses V1 API endpoint
- V2 contextual adding is handled automatically by the V1 endpoint when using appropriate parameters

### Future Features
- Multimodal Support
- Memory Customization
- Custom Categories & Instructions
- Direct Import/Export
- Async Client
- Webhooks
- Graph Memory
- REST API Server
- OpenAI Compatibility
- Custom Prompts


