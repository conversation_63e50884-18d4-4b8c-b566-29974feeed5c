from collections.abc import Generator
from typing import Any
import json
import httpx
import time
from dify_plugin import Tool
from dify_plugin.entities.tool import ToolInvokeMessage

class Mem0V2Tool(Tool):
    def _invoke(self, tool_parameters: dict[str, Any]) -> Generator[ToolInvokeMessage, None, None]:
        # Get API key from credentials
        api_key = self.runtime.credentials["mem0_api_key"]

        # Get API URL from credentials or use default
        api_url = self.runtime.credentials.get("mem0_api_url", "http://localhost:8000")
        if not api_url or api_url.strip() == "":
            api_url = "http://localhost:8000"
        api_url = api_url.rstrip("/")

        # Get required parameters
        user_content = tool_parameters.get("user", "")
        assistant_content = tool_parameters.get("assistant", "")
        user_id = tool_parameters.get("user_id", "")

        # Validate required parameters
        if not user_content:
            yield self.create_text_message("Error: User message is required.")
            return
        if not assistant_content:
            yield self.create_text_message("Error: Assistant response is required.")
            return
        if not user_id:
            yield self.create_text_message("Error: User ID is required.")
            return

        # Format messages
        messages = [
            {"role": "user", "content": user_content},
            {"role": "assistant", "content": assistant_content}
        ]

        # Prepare V2 payload
        payload = {
            "messages": messages,
            "user_id": user_id
        }

        # Add optional parameters
        if tool_parameters.get("agent_id"):
            payload["agent_id"] = tool_parameters["agent_id"]

        if tool_parameters.get("run_id"):
            payload["run_id"] = tool_parameters["run_id"]

        # Handle inference parameter (default to True for V2)
        infer = tool_parameters.get("infer", True)
        payload["infer"] = infer

        # Handle metadata
        if tool_parameters.get("metadata"):
            try:
                metadata = json.loads(tool_parameters["metadata"])
                payload["metadata"] = metadata
            except json.JSONDecodeError as e:
                error_message = f"Invalid JSON in metadata: {str(e)}"
                yield self.create_json_message({
                    "status": "error",
                    "error": error_message
                })
                yield self.create_text_message(f"Failed to add memory: {error_message}")
                return

        # Make HTTP request to mem0 V1 API (V2 add not available in our project)
        # Use V1 endpoint but with V2 enhanced parameters
        try:
            response = httpx.post(
                f"{api_url}/v1/memories",
                json=payload,
                headers={"Authorization": f"Token {api_key}"},
                timeout=30  # 增加超时时间以适应Mem0的增强记忆处理
            )

            response.raise_for_status()
            result = response.json()

            # Handle V2 response format
            memory_ids = []
            memories_data = []

            # V2 API returns: {"memories": [...]}
            if isinstance(result, dict) and "memories" in result:
                memories = result["memories"]

                for memory in memories:
                    if isinstance(memory, dict):
                        memory_id = memory.get("id")
                        if memory_id:
                            memory_ids.append(memory_id)

                        memory_data = {
                            "id": memory.get("id", "unknown"),
                            "memory": memory.get("memory", ""),
                            "categories": memory.get("categories", []),
                            "created_at": memory.get("created_at", ""),
                            "updated_at": memory.get("updated_at", "")
                        }
                        memories_data.append(memory_data)

            # Return JSON format
            yield self.create_json_message({
                "status": "success",
                "api_version": "v2",
                "messages": messages,
                "memory_ids": memory_ids,
                "memories": memories_data,
                "inference_enabled": infer
            })

            # Return text format
            text_response = "Memory added successfully using V2 API\n\n"
            text_response += "Added messages:\n"
            for msg in messages:
                text_response += f"- {msg['role']}: {msg['content']}\n"

            text_response += f"\nInference enabled: {infer}\n"

            if memory_ids:
                text_response += f"Memory IDs: {', '.join(memory_ids)}\n"

            if memories_data:
                text_response += "\nExtracted memories:\n"
                for i, mem in enumerate(memories_data, 1):
                    text_response += f"{i}. {mem['memory']}\n"
                    if mem['categories']:
                        text_response += f"   Categories: {', '.join(mem['categories'])}\n"

            yield self.create_text_message(text_response)

        except httpx.HTTPStatusError as e:
            error_message = f"HTTP error: {e.response.status_code}"
            try:
                error_data = e.response.json()
                if "detail" in error_data:
                    error_message = f"Error: {error_data['detail']}"
                elif "error" in error_data:
                    error_message = f"Error: {error_data['error']}"
            except:
                pass

            yield self.create_json_message({
                "status": "error",
                "api_version": "v2",
                "error": error_message
            })

            yield self.create_text_message(f"Failed to add memory (V2): {error_message}")

        except Exception as e:
            error_message = f"Error: {str(e)}"

            yield self.create_json_message({
                "status": "error",
                "api_version": "v2",
                "error": error_message
            })

            yield self.create_text_message(f"Failed to add memory (V2): {error_message}")
