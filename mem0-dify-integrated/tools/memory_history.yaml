identity:
  name: mem<PERSON>ai_memory_history
  author: blue
  label:
    en_US: Memory History
    zh_Hans: 记忆历史
    pt_BR: Histórico de Memória
    ja_JP: メモリ履歴
description:
  human:
    en_US: Retrieve the version history and changes of a specific memory in Mem0.
    zh_Hans: 检索Mem0中特定记忆的版本历史和变更记录。
    pt_BR: Recuperar o histórico de versões e alterações de uma memória específica no Mem0.
    ja_JP: Mem0内の特定のメモリのバージョン履歴と変更記録を取得します。
  llm: This tool retrieves the complete version history of a specific memory, showing all changes, updates, and modifications made over time. Useful for tracking memory evolution and understanding how memories have been modified.

parameters:
  - name: memory_id
    type: string
    required: true
    label:
      en_US: Memory ID
      zh_Hans: 记忆ID
      pt_BR: ID da Memória
      ja_JP: メモリID
    human_description:
      en_US: The unique identifier of the memory whose history to retrieve
      zh_Hans: 要检索历史的记忆的唯一标识符
      pt_BR: O identificador único da memória cujo histórico deve ser recuperado
      ja_JP: 履歴を取得するメモリの一意識別子
    llm_description: The unique identifier (ID) of the memory whose version history should be retrieved. This ID is typically obtained from previous memory operations.
    form: llm
extra:
  python:
    source: tools/memory_history.py
