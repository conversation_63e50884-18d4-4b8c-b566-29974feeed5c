from collections.abc import Generator
from typing import Any
import httpx
import time
from dify_plugin import Tool
from dify_plugin.entities.tool import ToolInvokeMessage

class MemoryHistoryTool(Tool):
    def _invoke(self, tool_parameters: dict[str, Any]) -> Generator[ToolInvokeMessage, None, None]:
        # Get API key from credentials
        api_key = self.runtime.credentials["mem0_api_key"]
        
        # Get API URL from credentials or use default (consistent with provider/mem0.yaml)

        api_url = self.runtime.credentials.get("mem0_api_url", "http://localhost:8000")

        if not api_url or api_url.strip() == "":

            api_url = "http://localhost:8000"

        api_url = api_url.rstrip("/")
        
        # Extract parameters
        memory_id = tool_parameters["memory_id"]
        
        # Validate memory ID
        if not memory_id or not memory_id.strip():
            yield self.create_json_message({
                "status": "error",
                "error": "Memory ID cannot be empty"
            })
            yield self.create_text_message("Error: Memory ID cannot be empty")
            return
        
        memory_id = memory_id.strip()

        # 记录历史查询操作        
        # 计时开始
        start_time = time.time()
        
        # Make HTTP request to get memory history
        try:
            response = httpx.get(
                f"{api_url}/v1/memories/{memory_id}/history",
                headers={"Authorization": f"Token {api_key}"},
                timeout=15  # 历史查询相对简单
            )
            
            # 计算请求耗时
            request_time = time.time() - start_time
            response.raise_for_status()
            
            # Parse response
            response_data = response.json()
            
            # Process history data
            history_entries = []
            
            if isinstance(response_data, dict):
                if "history" in response_data:
                    history_entries = response_data["history"]
                elif "versions" in response_data:
                    history_entries = response_data["versions"]
                else:
                    # Single entry format
                    history_entries = [response_data]
            elif isinstance(response_data, list):
                history_entries = response_data
            
            # Process each history entry
            processed_history = []
            for entry in history_entries:
                if isinstance(entry, dict):
                    item = {
                        "version": entry.get("version", "unknown"),
                        "memory": entry.get("memory", ""),
                        "score": entry.get("score", 0.0),
                        "categories": entry.get("categories", []),
                        "created_at": entry.get("created_at", ""),
                        "updated_at": entry.get("updated_at", ""),
                        "metadata": entry.get("metadata", {}),
                        "change_type": entry.get("change_type", "unknown"),
                        "changed_by": entry.get("changed_by", "system")
                    }
                    processed_history.append(item)
            
            # 记录历史查询结果
            # 计算总耗时
            total_time = time.time() - start_time
            yield self.create_json_message({
                "status": "success",
                "memory_id": memory_id,
                "history_count": len(processed_history),
                "history": processed_history,
                "original_response": response_data
            })
            
            # Return text format
            text_response = f"Memory history retrieved successfully.\n\n"
            text_response += f"Memory ID: {memory_id}\n"
            text_response += f"History Versions: {len(processed_history)}\n\n"
            
            if processed_history:
                text_response += "Version History:\n"
                for idx, entry in enumerate(processed_history, 1):
                    text_response += f"\n{idx}. Version: {entry['version']}"
                    text_response += f"\n   Memory: {entry['memory'][:100]}{'...' if len(entry['memory']) > 100 else ''}"
                    text_response += f"\n   Score: {entry['score']:.2f}"
                    text_response += f"\n   Categories: {', '.join(entry.get('categories', []))}"
                    text_response += f"\n   Change Type: {entry.get('change_type', 'N/A')}"
                    text_response += f"\n   Changed By: {entry.get('changed_by', 'N/A')}"
                    text_response += f"\n   Updated At: {entry.get('updated_at', 'N/A')}"
                    if entry.get('metadata'):
                        text_response += f"\n   Metadata: {entry['metadata']}"
            else:
                text_response += "\nNo history found for this memory."
                
            yield self.create_text_message(text_response)
            
        except httpx.HTTPStatusError as e:
            error_message = f"HTTP error: {e.response.status_code}"
            
            # Handle specific error cases
            if e.response.status_code == 404:
                error_message = f"Memory not found or no history available: {memory_id}"
            elif e.response.status_code == 403:
                error_message = "Permission denied: Cannot access memory history"
            elif e.response.status_code == 401:
                error_message = "Authentication failed: Invalid API key"
            else:
                try:
                    error_data = e.response.json()
                    if "detail" in error_data:
                        error_message = f"Error: {error_data['detail']}"
                except:
                    pass            
            yield self.create_json_message({
                "status": "error",
                "memory_id": memory_id,
                "error": error_message,
                "status_code": e.response.status_code
            })
            
            yield self.create_text_message(f"Failed to get memory history: {error_message}\n\nMemory ID: {memory_id}")
            
        except Exception as e:
            error_message = f"Error: {str(e)}"            
            yield self.create_json_message({
                "status": "error",
                "memory_id": memory_id,
                "error": error_message
            })
            
            yield self.create_text_message(f"Failed to get memory history: {error_message}\n\nMemory ID: {memory_id}")
