identity:
  name: multimodal_add_mem0ai
  author: blue
  label:
    en_US: Add Multimodal Memory
    zh_Hans: 添加多模态记忆
    pt_BR: Adicionar Memória Multimodal
    ja_JP: マルチモーダルメモリ追加
description:
  human:
    en_US: Add multimodal memories (text, images, audio) to Mem0 using conversation messages format.
    zh_Hans: 使用对话消息格式向Mem0添加多模态记忆（文本、图像、音频）。
    pt_BR: Adicionar memórias multimodais (texto, imagens, áudio) ao Mem0 usando formato de mensagens de conversa.
    ja_JP: 会話メッセージ形式を使用してMem0にマルチモーダルメモリ（テキスト、画像、音声）を追加します。
  llm: This tool adds multimodal memories to Mem0 using a conversation messages format. It supports text, images, audio, and other media types. The messages should be in OpenAI chat format.

parameters:
  - name: messages
    type: string
    required: true
    label:
      en_US: Messages
      zh_Hans: 消息
      pt_BR: Mensagens
      ja_JP: メッセージ
    human_description:
      en_US: 'JSON array of conversation messages in OpenAI format (e.g., [{"role": "user", "content": "text"}, {"role": "assistant", "content": "response"}])'
      zh_Hans: 'OpenAI格式的对话消息JSON数组（例如：[{"role": "user", "content": "text"}, {"role": "assistant", "content": "response"}]）'
      pt_BR: 'Array JSON de mensagens de conversa no formato OpenAI (ex: [{"role": "user", "content": "text"}, {"role": "assistant", "content": "response"}])'
      ja_JP: 'OpenAI形式の会話メッセージのJSON配列（例：[{"role": "user", "content": "text"}, {"role": "assistant", "content": "response"}]）'
    llm_description: A JSON array of conversation messages in OpenAI chat format. Each message should have 'role' (user/assistant/system) and 'content' fields. Supports multimodal content including text, images, and audio.
    form: llm

  - name: user_id
    type: string
    required: false
    label:
      en_US: User ID
      zh_Hans: 用户ID
      pt_BR: ID do Usuário
      ja_JP: ユーザーID
    human_description:
      en_US: Unique identifier for the user
      zh_Hans: 用户的唯一标识符
      pt_BR: Identificador único do usuário
      ja_JP: ユーザーの一意識別子
    llm_description: The unique identifier for the user whose memories are being added.
    form: llm

  - name: agent_id
    type: string
    required: false
    label:
      en_US: Agent ID
      zh_Hans: 代理ID
      pt_BR: ID do Agente
      ja_JP: エージェントID
    human_description:
      en_US: Unique identifier for the AI agent
      zh_Hans: AI代理的唯一标识符
      pt_BR: Identificador único do agente de IA
      ja_JP: AIエージェントの一意識別子
    llm_description: The unique identifier for the AI agent whose memories are being added.
    form: llm

  - name: run_id
    type: string
    required: false
    label:
      en_US: Run ID
      zh_Hans: 运行ID
      pt_BR: ID da Execução
      ja_JP: 実行ID
    human_description:
      en_US: Unique identifier for the conversation session
      zh_Hans: 对话会话的唯一标识符
      pt_BR: Identificador único da sessão de conversa
      ja_JP: 会話セッションの一意識別子
    llm_description: The unique identifier for the conversation session.
    form: llm

  - name: metadata
    type: string
    required: false
    label:
      en_US: Metadata
      zh_Hans: 元数据
      pt_BR: Metadados
      ja_JP: メタデータ
    human_description:
      en_US: 'Additional metadata as JSON string (e.g., {"source": "chat", "priority": "high"})'
      zh_Hans: '作为JSON字符串的附加元数据（例如：{"source": "chat", "priority": "high"}）'
      pt_BR: 'Metadados adicionais como string JSON (ex: {"source": "chat", "priority": "high"})'
      ja_JP: 'JSON文字列としての追加メタデータ（例：{"source": "chat", "priority": "high"}）'
    llm_description: Additional metadata to associate with the memories as a JSON string.
    form: llm
extra:
  python:
    source: tools/multimodal_add.py
