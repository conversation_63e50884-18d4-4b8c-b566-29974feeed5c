#!/usr/bin/env python3
"""
测试 v1 API 搜索功能，检查 user_id 为 "root" 搜索"小红"时是否返回重复内容
"""

import requests
import json
import sys
from typing import List, Dict, Any
from collections import defaultdict

# API 配置
BASE_URL = "http://localhost:8000"
V1_SEARCH_URL = f"{BASE_URL}/v1/memories/search/"
V1_ADD_URL = f"{BASE_URL}/v1/memories/"
HEALTH_URL = f"{BASE_URL}/health/"

def check_server_status():
    """检查服务器是否运行"""
    try:
        response = requests.get(HEALTH_URL, timeout=5)
        if response.status_code == 200:
            print("✓ 服务器运行正常")
            return True
        else:
            print(f"✗ 服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到服务器，请确保服务器在 http://localhost:8000 运行")
        return False
    except Exception as e:
        print(f"✗ 检查服务器状态时出错: {e}")
        return False

def add_test_memories():
    """添加测试记忆数据"""
    test_memories = [
        {
            "text": "小红是一个聪明的学生，喜欢数学和科学",
            "user_id": "root",
            "metadata": {"category": "person", "name": "小红"}
        },
        {
            "text": "小红今天参加了数学竞赛，获得了第一名",
            "user_id": "root", 
            "metadata": {"category": "achievement", "name": "小红"}
        },
        {
            "text": "小红的爱好是阅读和画画",
            "user_id": "root",
            "metadata": {"category": "hobby", "name": "小红"}
        },
        {
            "text": "小红住在北京，是高中生",
            "user_id": "root",
            "metadata": {"category": "basic_info", "name": "小红"}
        },
        {
            "text": "小红的朋友叫小明，他们经常一起学习",
            "user_id": "root",
            "metadata": {"category": "relationship", "name": "小红"}
        }
    ]
    
    print("正在添加测试记忆数据...")
    added_memories = []
    
    for memory in test_memories:
        try:
            response = requests.post(V1_ADD_URL, json=memory)
            if response.status_code == 200:
                result = response.json()
                added_memories.append(result)
                print(f"✓ 添加成功: {memory['text'][:30]}...")
            else:
                print(f"✗ 添加失败: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"✗ 添加异常: {e}")
    
    return added_memories

def search_memories(query: str, user_id: str, **kwargs) -> Dict[str, Any]:
    """搜索记忆"""
    search_data = {
        "query": query,
        "user_id": user_id,
        **kwargs
    }
    
    try:
        response = requests.post(V1_SEARCH_URL, json=search_data)
        if response.status_code == 200:
            return response.json()
        else:
            print(f"搜索失败: {response.status_code} - {response.text}")
            return {}
    except Exception as e:
        print(f"搜索异常: {e}")
        return {}

def check_duplicates(search_results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """检查搜索结果中的重复内容"""
    duplicate_info = {
        "has_duplicates": False,
        "duplicate_groups": [],
        "total_results": len(search_results),
        "unique_results": 0,
        "duplicate_count": 0
    }
    
    # 按内容分组检查重复
    content_groups = defaultdict(list)
    id_groups = defaultdict(list)
    
    for i, result in enumerate(search_results):
        # 按记忆内容分组
        if isinstance(result, dict):
            memory_text = result.get('memory', result.get('text', ''))
            memory_id = result.get('id', result.get('memory_id', f'unknown_{i}'))
            
            content_groups[memory_text].append({
                'index': i,
                'id': memory_id,
                'result': result
            })
            
            id_groups[memory_id].append({
                'index': i,
                'content': memory_text,
                'result': result
            })
    
    # 检查内容重复
    for content, items in content_groups.items():
        if len(items) > 1:
            duplicate_info["has_duplicates"] = True
            duplicate_info["duplicate_groups"].append({
                "type": "content",
                "content": content,
                "count": len(items),
                "items": items
            })
            duplicate_info["duplicate_count"] += len(items) - 1
    
    # 检查ID重复
    for memory_id, items in id_groups.items():
        if len(items) > 1:
            duplicate_info["has_duplicates"] = True
            duplicate_info["duplicate_groups"].append({
                "type": "id",
                "id": memory_id,
                "count": len(items),
                "items": items
            })
    
    duplicate_info["unique_results"] = len(content_groups)
    
    return duplicate_info

def print_search_results(results: List[Dict[str, Any]], title: str):
    """打印搜索结果"""
    print(f"\n=== {title} ===")
    print(f"结果数量: {len(results)}")
    
    for i, result in enumerate(results):
        print(f"\n结果 {i+1}:")
        if isinstance(result, dict):
            memory_text = result.get('memory', result.get('text', 'N/A'))
            memory_id = result.get('id', result.get('memory_id', 'N/A'))
            score = result.get('score', 'N/A')
            
            print(f"  ID: {memory_id}")
            print(f"  内容: {memory_text}")
            print(f"  分数: {score}")
            
            # 打印其他字段
            for key, value in result.items():
                if key not in ['memory', 'text', 'id', 'memory_id', 'score']:
                    print(f"  {key}: {value}")
        else:
            print(f"  {result}")

def print_duplicate_analysis(duplicate_info: Dict[str, Any]):
    """打印重复分析结果"""
    print(f"\n=== 重复内容分析 ===")
    print(f"总结果数: {duplicate_info['total_results']}")
    print(f"唯一结果数: {duplicate_info['unique_results']}")
    print(f"重复数量: {duplicate_info['duplicate_count']}")
    print(f"是否有重复: {'是' if duplicate_info['has_duplicates'] else '否'}")
    
    if duplicate_info['has_duplicates']:
        print(f"\n重复组详情:")
        for i, group in enumerate(duplicate_info['duplicate_groups']):
            print(f"\n重复组 {i+1} ({group['type']}):")
            if group['type'] == 'content':
                print(f"  重复内容: {group['content'][:100]}...")
                print(f"  重复次数: {group['count']}")
                for item in group['items']:
                    print(f"    - 索引 {item['index']}, ID: {item['id']}")
            elif group['type'] == 'id':
                print(f"  重复ID: {group['id']}")
                print(f"  重复次数: {group['count']}")
                for item in group['items']:
                    print(f"    - 索引 {item['index']}, 内容: {item['content'][:50]}...")

def main():
    """主测试函数"""
    print("开始测试 v1 API 搜索功能...")

    # 0. 检查服务器状态
    if not check_server_status():
        print("服务器未运行，请先启动服务器")
        return

    # 1. 添加测试数据
    added_memories = add_test_memories()
    if not added_memories:
        print("无法添加测试数据，退出测试")
        return
    
    print(f"\n成功添加 {len(added_memories)} 条测试记忆")
    
    # 2. 测试搜索"小红"
    print(f"\n开始搜索 user_id='root' 的'小红'相关记忆...")
    
    # 基础搜索
    search_result = search_memories("小红", "root")
    
    if not search_result:
        print("搜索失败或无结果")
        return
    
    # 提取搜索结果
    results = []
    if isinstance(search_result, dict):
        if 'results' in search_result:
            results = search_result['results']
        elif isinstance(search_result, list):
            results = search_result
        else:
            # 可能是单个结果或其他格式
            results = [search_result]
    elif isinstance(search_result, list):
        results = search_result
    
    # 打印搜索结果
    print_search_results(results, "搜索结果")
    
    # 3. 检查重复
    duplicate_info = check_duplicates(results)
    print_duplicate_analysis(duplicate_info)
    
    # 4. 测试不同参数的搜索
    print(f"\n=== 测试不同搜索参数 ===")
    
    # 测试限制结果数量
    limited_result = search_memories("小红", "root", limit=3)
    if limited_result:
        limited_results = limited_result.get('results', limited_result if isinstance(limited_result, list) else [])
        print(f"\n限制3个结果的搜索:")
        print_search_results(limited_results, "限制结果搜索")
        limited_duplicate_info = check_duplicates(limited_results)
        print_duplicate_analysis(limited_duplicate_info)
    
    # 测试带过滤器的搜索
    filtered_result = search_memories("小红", "root", filters={"category": "person"})
    if filtered_result:
        filtered_results = filtered_result.get('results', filtered_result if isinstance(filtered_result, list) else [])
        print(f"\n带过滤器的搜索:")
        print_search_results(filtered_results, "过滤器搜索")
        filtered_duplicate_info = check_duplicates(filtered_results)
        print_duplicate_analysis(filtered_duplicate_info)
    
    # 5. 总结
    print(f"\n=== 测试总结 ===")
    if duplicate_info['has_duplicates']:
        print("⚠️  发现重复内容！")
        print(f"   - 总结果数: {duplicate_info['total_results']}")
        print(f"   - 唯一结果数: {duplicate_info['unique_results']}")
        print(f"   - 重复数量: {duplicate_info['duplicate_count']}")
        print(f"   - 重复组数: {len(duplicate_info['duplicate_groups'])}")
    else:
        print("✓ 未发现重复内容")
        print(f"   - 总结果数: {duplicate_info['total_results']}")
        print(f"   - 所有结果都是唯一的")

if __name__ == "__main__":
    main()
