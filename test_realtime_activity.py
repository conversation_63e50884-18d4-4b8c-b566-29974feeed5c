#!/usr/bin/env python3
"""
测试实时活动更新
"""

import requests
import json
import time
from datetime import datetime

def create_test_memory():
    """创建一个测试记忆来生成新的活动"""
    test_memory = {
        "messages": [
            {
                "role": "user", 
                "content": f"测试活动时间线实时更新 - {datetime.now().strftime('%H:%M:%S')}"
            }
        ],
        "user_id": "test_user_timeline",
        "agent_id": "test_agent",
        "run_id": "test_run"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/v1/memories/",
            json=test_memory,
            headers={"Content-Type": "application/json"}
        )
        if response.status_code == 200:
            data = response.json()
            # 处理不同的响应格式
            if isinstance(data, dict):
                results = data.get('results', [])
                if results and len(results) > 0:
                    memory_id = results[0].get('id', 'unknown') if isinstance(results[0], dict) else 'unknown'
                else:
                    memory_id = data.get('id', 'unknown')
            else:
                memory_id = 'unknown'
            print(f"✓ 创建测试记忆成功: {memory_id}")
            return True
        else:
            print(f"✗ 创建测试记忆失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"✗ 创建测试记忆异常: {e}")
        return False

def get_latest_activities(limit=3):
    """获取最新的活动"""
    try:
        response = requests.get(f"http://localhost:8000/v1/activities/?limit={limit}")
        if response.status_code == 200:
            data = response.json()
            return data.get('activities', [])
        else:
            print(f"✗ 获取活动失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"✗ 获取活动异常: {e}")
        return []

def main():
    print("=" * 60)
    print("Mem0 UI 实时活动更新测试")
    print("=" * 60)
    
    # 1. 获取当前最新活动作为基准
    print("\n📋 获取当前最新活动:")
    initial_activities = get_latest_activities(3)
    if initial_activities:
        print(f"   当前最新活动时间: {initial_activities[0].get('timestamp', 'unknown')}")
        print(f"   当前最新活动ID: {initial_activities[0].get('id', 'unknown')}")
    else:
        print("   ✗ 无法获取当前活动")
        return
    
    # 2. 创建新的测试记忆
    print("\n🆕 创建新的测试记忆:")
    if not create_test_memory():
        return
    
    # 3. 等待一下让数据库更新
    print("\n⏳ 等待数据库更新...")
    time.sleep(2)
    
    # 4. 检查是否有新的活动
    print("\n🔍 检查新活动:")
    new_activities = get_latest_activities(3)
    if new_activities:
        latest_activity = new_activities[0]
        if latest_activity.get('id') != initial_activities[0].get('id'):
            print("   ✓ 检测到新活动!")
            print(f"   新活动时间: {latest_activity.get('timestamp', 'unknown')}")
            print(f"   新活动ID: {latest_activity.get('id', 'unknown')}")
            print(f"   操作类型: {latest_activity.get('operation', 'unknown')}")
            print(f"   用户ID: {latest_activity.get('user_id', 'unknown')}")
            print(f"   详情: {latest_activity.get('details', 'unknown')}")
        else:
            print("   ⚠️ 未检测到新活动，可能需要更长时间同步")
    else:
        print("   ✗ 无法获取新活动")
    
    # 5. 测试前端自动刷新建议
    print("\n🎨 前端测试建议:")
    print("   1. 打开浏览器访问 mem0_ui 应用")
    print("   2. 导航到活动时间线页面")
    print("   3. 观察是否显示刚创建的测试活动")
    print("   4. 等待30秒观察自动刷新是否工作")
    print("   5. 手动点击刷新按钮测试手动刷新")
    
    print("\n✅ 测试完成!")
    print("   如果前端仍未显示最新活动，请检查:")
    print("   • 浏览器控制台错误信息")
    print("   • 网络连接状态")
    print("   • 前端应用是否正在运行")
    print("   • API端点配置是否正确")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
