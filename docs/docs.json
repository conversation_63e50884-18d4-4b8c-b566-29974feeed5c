{"$schema": "https://mintlify.com/docs.json", "theme": "maple", "name": "Mem0", "description": "Mem0 is a self-improving memory layer for LLM applications, enabling personalized AI experiences that save costs and delight users.", "colors": {"primary": "#6c60f0", "light": "#E6FFA2", "dark": "#a3df02"}, "favicon": "/logo/favicon.png", "navigation": {"anchors": [{"anchor": "Documentation", "icon": "book-open", "tabs": [{"tab": "Documentation", "groups": [{"group": "Getting Started", "icon": "rocket", "pages": ["what-is-mem0", "quickstart", "faqs"]}, {"group": "Core Concepts", "icon": "brain", "pages": ["core-concepts/memory-types", {"group": "Memory Operations", "icon": "gear", "pages": ["core-concepts/memory-operations/add", "core-concepts/memory-operations/search", "core-concepts/memory-operations/update", "core-concepts/memory-operations/delete"]}]}, {"group": "Platform", "icon": "cogs", "pages": ["platform/overview", "platform/quickstart", {"group": "Features", "icon": "star", "pages": ["platform/features/platform-overview", "platform/features/contextual-add", "platform/features/async-client", "platform/features/advanced-retrieval", "platform/features/criteria-retrieval", "platform/features/multimodal-support", "platform/features/selective-memory", "platform/features/custom-categories", "platform/features/custom-instructions", "platform/features/direct-import", "platform/features/memory-export", "platform/features/timestamp", "platform/features/expiration-date", "platform/features/webhooks", "platform/features/feedback-mechanism", "platform/features/group-chat"]}]}, {"group": "Open Source", "icon": "code-branch", "pages": ["open-source/overview", "open-source/python-quickstart", "open-source/node-quickstart", {"group": "Features", "icon": "star", "pages": ["open-source/features/async-memory", "open-source/features/openai_compatibility", "open-source/features/custom-fact-extraction-prompt", "open-source/features/custom-update-memory-prompt", "open-source/features/multimodal-support", "open-source/features/rest-api"]}, {"group": "Graph Memory", "icon": "spider-web", "pages": ["open-source/graph_memory/overview", "open-source/graph_memory/features"]}, {"group": "LLMs", "icon": "brain", "pages": ["components/llms/overview", "components/llms/config", {"group": "Supported LLMs", "icon": "list", "pages": ["components/llms/models/openai", "components/llms/models/anthropic", "components/llms/models/azure_openai", "components/llms/models/ollama", "components/llms/models/together", "components/llms/models/groq", "components/llms/models/litellm", "components/llms/models/mistral_AI", "components/llms/models/google_AI", "components/llms/models/aws_bedrock", "components/llms/models/deepseek", "components/llms/models/xAI", "components/llms/models/sarvam", "components/llms/models/lmstudio", "components/llms/models/langchain", "components/llms/models/vllm"]}]}, {"group": "Vector Databases", "icon": "database", "pages": ["components/vectordbs/overview", "components/vectordbs/config", {"group": "Supported Vector Databases", "icon": "server", "pages": ["components/vectordbs/dbs/qdrant", "components/vectordbs/dbs/chroma", "components/vectordbs/dbs/pgvector", "components/vectordbs/dbs/milvus", "components/vectordbs/dbs/pinecone", "components/vectordbs/dbs/mongodb", "components/vectordbs/dbs/azure", "components/vectordbs/dbs/redis", "components/vectordbs/dbs/elasticsearch", "components/vectordbs/dbs/opensearch", "components/vectordbs/dbs/supabase", "components/vectordbs/dbs/vertex_ai", "components/vectordbs/dbs/weaviate", "components/vectordbs/dbs/faiss", "components/vectordbs/dbs/langchain", "components/vectordbs/dbs/baidu"]}]}, {"group": "Embedding Models", "icon": "layer-group", "pages": ["components/embedders/overview", "components/embedders/config", {"group": "Supported Embedding Models", "icon": "list", "pages": ["components/embedders/models/openai", "components/embedders/models/azure_openai", "components/embedders/models/ollama", "components/embedders/models/huggingface", "components/embedders/models/vertexai", "components/embedders/models/google_AI", "components/embedders/models/lmstudio", "components/embedders/models/together", "components/embedders/models/langchain", "components/embedders/models/aws_bedrock"]}]}]}, {"group": "Contribution", "icon": "handshake", "pages": ["contributing/development", "contributing/documentation"]}]}, {"tab": "OpenMemory", "icon": "square-terminal", "pages": ["openmemory/overview", "openmemory/quickstart", "openmemory/integrations"]}, {"tab": "Examples", "groups": [{"group": "💡 Examples", "icon": "lightbulb", "pages": ["examples", "examples/aws_example", "examples/mem0-demo", "examples/ai_companion_js", "examples/collaborative-task-agent", "examples/llamaindex-multiagent-learning-system", "examples/personalized-search-tavily-mem0", "examples/eliza_os", "examples/mem0-mastra", "examples/mem0-with-ollama", "examples/personal-ai-tutor", "examples/customer-support-agent", "examples/personal-travel-assistant", "examples/llama-index-mem0", "examples/chrome-extension", "examples/memory-guided-content-writing", "examples/multimodal-demo", "examples/personalized-deep-research", "examples/mem0-agentic-tool", "examples/openai-inbuilt-tools", "examples/mem0-openai-voice-demo", "examples/mem0-google-adk-healthcare-assistant", "examples/email_processing", "examples/youtube-assistant"]}]}, {"tab": "Integrations", "groups": [{"group": "Integrations", "icon": "plug", "pages": ["integrations", "integrations/langchain", "integrations/langgraph", "integrations/llama-index", "integrations/agno", "integrations/autogen", "integrations/crewai", "integrations/openai-agents-sdk", "integrations/google-ai-adk", "integrations/mastra", "integrations/vercel-ai-sdk", "integrations/livekit", "integrations/pipecat", "integrations/elevenlabs", "integrations/aws-bedrock", "integrations/flowise", "integrations/langchain-tools", "integrations/agentops", "integrations/keywords", "integrations/dify", "integrations/raycast"]}]}, {"tab": "API Reference", "icon": "square-terminal", "groups": [{"group": "API Reference", "icon": "terminal", "pages": ["api-reference", {"group": "Memory APIs", "icon": "microchip", "pages": ["api-reference/memory/add-memories", "api-reference/memory/v2-search-memories", "api-reference/memory/v1-search-memories", "api-reference/memory/v2-get-memories", "api-reference/memory/v1-get-memories", "api-reference/memory/history-memory", "api-reference/memory/get-memory", "api-reference/memory/update-memory", "api-reference/memory/batch-update", "api-reference/memory/delete-memory", "api-reference/memory/batch-delete", "api-reference/memory/delete-memories", "api-reference/memory/create-memory-export", "api-reference/memory/get-memory-export", "api-reference/memory/feedback"]}, {"group": "Entities APIs", "icon": "users", "pages": ["api-reference/entities/get-users", "api-reference/entities/delete-user"]}, {"group": "Organizations APIs", "icon": "building", "pages": ["api-reference/organization/create-org", "api-reference/organization/get-orgs", "api-reference/organization/get-org", "api-reference/organization/get-org-members", "api-reference/organization/add-org-member", "api-reference/organization/delete-org"]}, {"group": "Project APIs", "icon": "folder", "pages": ["api-reference/project/create-project", "api-reference/project/get-projects", "api-reference/project/get-project", "api-reference/project/get-project-members", "api-reference/project/add-project-member", "api-reference/project/delete-project"]}, {"group": "Webhook APIs", "icon": "webhook", "pages": ["api-reference/webhook/create-webhook", "api-reference/webhook/get-webhook", "api-reference/webhook/update-webhook", "api-reference/webhook/delete-webhook"]}]}]}, {"tab": "Changelog", "icon": "clock", "groups": [{"group": "Product Updates", "icon": "rocket", "pages": ["changelog"]}]}]}]}, "logo": {"light": "/logo/light.svg", "dark": "/logo/dark.svg", "href": "https://github.com/mem0ai/mem0"}, "background": {"color": {"light": "#fff", "dark": "#0f1117"}}, "navbar": {"primary": {"type": "button", "label": "Your Dashboard", "href": "https://app.mem0.ai"}}, "footer": {"socials": {"discord": "https://mem0.dev/DiD", "x": "https://x.com/mem0ai", "github": "https://github.com/mem0ai", "linkedin": "https://www.linkedin.com/company/mem0/"}}, "integrations": {"posthog": {"apiKey": "phc_hgJkUVJFYtmaJqrvf6CYN67TIQ8yhXAkWzUn9AMU4yX", "apiHost": "https://mango.mem0.ai"}, "intercom": {"appId": "jjv2r0tt"}}}